from config.logger import setup_logging
from plugins_func.register import register_function, ToolType, ActionResponse, Action
from core.handle.iotHandle import send_iot_conn
from .validation import validate_movement_parameters
import asyncio

TAG = __name__
logger = setup_logging()

# 定义插件描述，供LLM理解函数的用途和参数
handle_movement_function_desc = {
    "type": "function",
    "function": {
        "name": "handle_movement",
        "description": "提供设备移动的原子能力。此函数执行单次基础移动指令：前进、后退、左转、右转、停止和跳舞。\n\n"
                       "🔄 复杂指令处理：\n"
                       "- 如果用户要求'连续移动两次3米'、'前进3米然后再前进3米'等复合动作，必须多次调用此函数\n"
                       "- 每次调用只能执行一个原子动作，复杂动作需要分解为多个步骤\n"
                       "- 例如：'连续移动两次3米' = 使用function_calls数组调用两次:\n"
                       "  [{\"name\": \"handle_movement\", \"arguments\": {\"action\": \"forward\", \"distance\": 300}},\n"
                       "   {\"name\": \"handle_movement\", \"arguments\": {\"action\": \"forward\", \"distance\": 300}}]\n"
                       "- 例如：'转左90度然后前进2米' = 两次调用:\n"
                       "  [{\"name\": \"handle_movement\", \"arguments\": {\"action\": \"left\", \"angle\": 90}},\n"
                       "   {\"name\": \"handle_movement\", \"arguments\": {\"action\": \"forward\", \"distance\": 200}}]\n\n"
                       "⚠️ 重要：在调用此函数前，你必须先检查用户要求是否在物理限制范围内！\n\n"
                       "🚫 绝对不要调用此函数如果：\n"
                       "- 用户要求转超过2圈（如'转三圈'、'turn 3 circles'、'转1080度'等）\n"
                       "- 用户要求速度超过100（如'全速'、'最快速度'可能暗示超过100）\n"
                       "- 用户要求移动时间超过20秒（如'移动30秒'、'走一分钟'等）\n"
                       "- 用户要求移动距离超过3米（如'走5米'、'前进500厘米'等）\n\n"
                       "✅ 遇到超出限制的要求时，直接回复限制信息，不要调用函数！\n"
                       "例如：用户说'转三圈'时，直接回答'抱歉，我一次最多只能转两圈（720度）'",
        "parameters": {
            "type": "object",
            "properties": {
                "action": {
                    "type": "string",
                    "description": "要执行的原子移动动作。支持: 'forward', 'backward', 'left', 'right', 'stop', 'dance'。",
                    "enum": ["forward", "backward", "left", "right", "stop", "dance"]
                },
                "speed": {
                    "type": "integer",
                    "description": "移动速度，范围从 0 到 100。这是一个硬性物理限制。如果用户要求的速度超过100，你应该直接回答‘我最快只能设置为100’。",
                    "default": 50,
                    "minimum": 0,
                    "maximum": 100
                },
                "time": {
                    "type": "integer",
                    "description": "动作的持续时间，单位为秒。适用于 'forward', 'backward', 'left', 'right'。范围从 0 到 20 秒。这是一个硬性物理限制。如果用户要求的时间超过20秒，你应该直接回答‘我一次最多只能移动20秒’。",
                    "minimum": 0,
                    "maximum": 20
                },
                "distance": {
                    "type": "integer",
                    "description": "移动的距离，单位为厘米。仅用于 'forward' 和 'backward' 动作。范围从 0 到 300 厘米。这是一个硬性物理限制。如果用户要求的距离超过300厘米，你应该直接回答‘我一次最多只能移动3米’。",
                    "minimum": 0,
                    "maximum": 300
                },
                "angle": {
                    "type": "integer",
                    "description": "旋转的角度，单位为度。仅用于 'left' 和 'right' 动作。范围从 0 到 720 度。这是一个硬性物理限制。如果用户要求的角度超过720度，你应该直接回答‘我一次最多只能转两圈（720度）’。",
                    "minimum": 0,
                    "maximum": 720
                }
            },
            "required": ["action"]
        }
    }
}


async def _execute_movement(conn, action, speed=50, time=None, distance=None, angle=None):
    """
    异步执行单个原子移动控制命令。
    此函数负责查找设备、映射方法并发送IoT指令。
    """
    logger.bind(tag=TAG).info(f"🎯 [EXECUTE_MOVEMENT] 开始执行原子移动命令")
    logger.bind(tag=TAG).info(f"📋 [EXECUTE_PARAMS] action={action}, speed={speed}, time={time}, distance={distance}, angle={angle}")

    # 自动查找支持移动的设备
    device_type = None
    for dev_name, descriptor in conn.iot_descriptors.items():
        if 'movement' in descriptor.capabilities:
            device_type = dev_name
            logger.bind(tag=TAG).info(f"🔍 [DEVICE_BY_CAP] 自动检测到支持 'movement' 能力的设备: {device_type}")
            break

    if not device_type:
        available_devices = list(conn.iot_descriptors.keys())
        logger.bind(tag=TAG).error(f"❌ [NO_MOVEMENT_DEVICE] 未找到支持移动的设备。可用设备: {available_devices}")
        raise ValueError("抱歉，我没有找到可以移动的设备。")

    # 动态映射action到实际的设备方法名
    device_methods = [method["name"] for method in conn.iot_descriptors[device_type].methods]
    action_method_mapping = {
        "forward": ["Forword", "Forward", "MoveForward", "GoForward", "前进"],
        "backward": ["Backword", "Backward", "MoveBackward", "GoBackward", "后退"],
        "left": ["Turn_left", "TurnLeft", "Left", "左转"],
        "right": ["Turn_right", "TurnRight", "Right", "右转"],
        "stop": ["Stop", "Halt", "Pause", "停止"],
        "dance": ["Dance", "StartDancing", "跳舞"],
    }

    method_name = next((m for m in action_method_mapping.get(action, []) if m in device_methods), None)

    if not method_name:
        logger.bind(tag=TAG).error(f"❌ [METHOD_ERROR] 设备 {device_type} 不支持动作: {action}。可用方法: {device_methods}")
        raise ValueError(f"抱歉，我不支持'{action}'这个动作。")

    # 构建参数
    if action in ["stop", "dance"]:
        params = {}
    else:
        params = {"speed": speed}
        if time is not None:
            params["time"] = int(time * 1000)
        if distance is not None:
            params["distance"] = distance
        if angle is not None:
            params["angle"] = angle

    logger.bind(tag=TAG).info(f"📦 [IOT_PARAMS] 构建IoT参数: {params}")
    logger.bind(tag=TAG).info(f"📡 [IOT_SEND] 发送IoT指令: 设备={device_type}, 方法={method_name}")

    # 发送控制命令
    await send_iot_conn(conn, device_type, method_name, params)
    logger.bind(tag=TAG).info(f"✅ [IOT_SEND] IoT指令发送成功")

    # 构建成功结果的描述
    result_parts = [f"action={action}"]
    if action not in ["stop", "dance"]:
        result_parts.append(f"speed={speed}")
    if time is not None and action not in ["stop", "dance"]:
        result_parts.append(f"time={time}s")
    if distance is not None:
        result_parts.append(f"distance={distance}cm")
    if angle is not None:
        result_parts.append(f"angle={angle}°")

    return ", ".join(result_parts)



@register_function('handle_movement', handle_movement_function_desc, ToolType.IOT_CTL)
def handle_movement(conn, action, speed=50, time=None, distance=None, angle=None):
    """
    处理设备移动的原子控制。

    此函数是设备移动控制的统一入口，其职责如下：
    1.  **参数验证**: 确保所有输入参数都在其物理约束范围内。如果验证失败，将抛出一个包含结构化错误信息的 `ValueError`。
    2.  **执行动作**: 调用 `_execute_movement` 来执行单个原子移动。
    3.  **结果反馈**: 根据执行结果返回成功或失败的 `ActionResponse`。

    **原子动作参数**:
    -   `forward`/`backward`: `speed`(0-100), `time`(0-20s) 或 `distance`(0-300cm)。
    -   `left`/`right`: `speed`(0-100), `time`(0-20s) 或 `angle`(0-720度)。
    -   `stop`: 无需额外参数。
    -   `dance`: 无需额外参数。

    **错误处理**:
    -   如果参数验证失败，将抛出 `ValueError`，其内容为包含详细错误信息的字典。
        示例: `{'error_code': 'PARAMETER_OUT_OF_RANGE', 'parameter': 'speed', 'value': 150, 'allowed_range': [0, 100]}`
    -   如果未找到支持移动的设备，将抛出 `ValueError`。
        示例: `{'error_code': 'NO_MOVEMENT_DEVICE_FOUND', 'available_devices': ['device1']}`
    -   如果设备不支持请求的动作，将抛出 `ValueError`。
        示例: `{'error_code': 'UNSUPPORTED_ACTION', 'device_type': 'device1', 'action': 'fly', 'available_methods': ['forward']}`

    :param conn: 连接对象。
    :param action: 要执行的原子动作。
    :param speed: 移动速度 (0-100)。
    :param time: 持续时间（秒）。
    :param distance: 移动距离（厘米）。
    :param angle: 旋转角度（度）。
    :return: ActionResponse 对象。
    """
    logger.bind(tag=TAG).info(f"🚀 [FUNCTION_CALL] handle_movement 被调用")
    logger.bind(tag=TAG).info(f"📋 [PARAMETERS] action={action}, speed={speed}, time={time}, distance={distance}, angle={angle}")

    try:
        # 1. 验证参数
        validate_movement_parameters(action=action, speed=speed, time=time, distance=distance, angle=angle)
        logger.bind(tag=TAG).info(f"✅ [VALIDATION] 参数验证通过")

        # 2. 检查设备连接状态
        if not conn.iot_descriptors:
            logger.bind(tag=TAG).warning(f"⚠️ [NO_DEVICES] 没有连接任何IoT设备")
            return ActionResponse(action=Action.ERROR, result="no_devices", response="抱歉，这个操作对我太复杂了，请重新描述一下好吗。")

        # 3. 执行原子动作
        future = asyncio.run_coroutine_threadsafe(
            _execute_movement(conn, action, speed, time, distance, angle), conn.loop
        )
        result_summary = future.result(timeout=15)

        logger.bind(tag=TAG).info(f"✅ [EXECUTION_SUCCESS] 移动控制成功: {result_summary}")
        return ActionResponse(action=Action.RESPONSE, result=result_summary, response=None)

    except ValueError as e:
        logger.bind(tag=TAG).error(f"❌ [VALIDATION_FAILURE] 参数验证失败: {str(e)}")

        # 直接使用验证函数返回的用户友好消息
        user_friendly_message = str(e)

        return ActionResponse(action=Action.ERROR, result=str(e), response=user_friendly_message)
    except Exception as e:
        logger.bind(tag=TAG).error(f"❌ [EXECUTION_FAILURE] 移动控制失败: {e}")
        # 针对未预期的异常，返回用户友好的错误消息
        user_friendly_message = "抱歉，移动控制出现了意外问题，请稍后重试。"
        return ActionResponse(action=Action.ERROR, result=str(e), response=user_friendly_message)